package diffRegister

import (
	"bytes"
	"fmt"
	"fwyytool/components"
	"fwyytool/helpers"
	"fwyytool/stru"
	"html"
	"io"
	"os"
	"strconv"
	"strings"
	"time"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	json "github.com/json-iterator/go"
	"github.com/spf13/cast"
)

type ChangeLog struct {
	Type string      `json:"type"`
	Path []string    `json:"path"`
	From interface{} `json:"from"`
	To   interface{} `json:"to"`
}

// DiffItem 差异项结构
type DiffItem struct {
	Path string      `json:"path"`     // JSON路径
	Type string      `json:"type"`     // 差异类型：create/update/delete
	From interface{} `json:"from"`     // 原值
	To   interface{} `json:"to"`       // 新值
	Raw  string      `json:"raw"`      // 原始字符串
}

// DiffStats 差异统计
type DiffStats struct {
	Total  int `json:"total"`  // 总差异数量
	Create int `json:"create"` // 新增数量
	Update int `json:"update"` // 修改数量
	Delete int `json:"delete"` // 删除数量
}

var ColourMap = map[string]string{
	"create": "#90EE90",
	"update": "#FED8B1",
	"delete": "#FFB6C1",
}

// HighlightRule 定义高亮规则（新增Description字段）
type HighlightRule struct {
	Path        []string // JSON路径
	Color       string   // 高亮颜色
	Description string   // 新增：规则描述
}

// GenerateJSONHTMLFromString 生成带描述的HTML
func GenerateJSONHTMLFromString(ctx *gin.Context, jsonStr string, rules []HighlightRule, id int64) (path string, err error) {
	var data interface{}
	if err = json.Unmarshal([]byte(jsonStr), &data); err != nil {
		return
	}

	currentTime := time.Now().Format("2006-01-02_15:04:05")
	fileName := fmt.Sprintf("diff_%d_%s", id, currentTime)
	filePath := "./" + fileName + ".html"
	file, err := os.Create(filePath)
	if err != nil {
		fmt.Println("Error creating file:", err)
		return
	}
	defer func() {
		file.Close()
		os.Remove(filePath)
	}()

	// 写入HTML模板（新增描述展示区）
	fmt.Fprint(file, `<!DOCTYPE html>
<html>
<head>
  <title>JSON Highlight with Descriptions</title>
        <meta charset="UTF-8">
  <style>
    pre { 
      font-family: Menlo, Consolas, monospace;
      background: #f8f8f8;
      padding: 20px;
      border-radius: 5px;
    }
    .legend {
      margin-bottom: 20px;
      padding: 15px;
      background: #fff;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
    .legend-item {
      display: flex;
      align-items: center;
      margin: 5px 0;
    }
    .color-box {
      width: 20px;
      height: 20px;
      margin-right: 10px;
      border: 1px solid #ccc;
    }
  </style>
</head>
<body>
`)

	// 生成说明图例
	generateLegend(file, rules)

	// 生成JSON内容
	fmt.Fprint(file, "<pre>\n")
	var buf bytes.Buffer
	generateJSON(data, []string{}, rules, &buf, 0)
	file.Write(buf.Bytes())
	fmt.Fprint(file, "</pre>\n</body>\n</html>")

	// upload cos
	path, _, err = helpers.UploadFile2Cos(ctx, filePath, fileName, "html", "fwyyDiffRes")
	if err != nil {
		return
	}

	return
}

// 生成说明图例
func generateLegend(w io.Writer, rules []HighlightRule) {
	fmt.Fprint(w, `<div class="legend"><h3>Diff 结果说明</h3>`)
	for _, rule := range rules {
		fmt.Fprintf(w, `
    <div class="legend-item">
      <div class="color-box" style="background-color: %s"></div>
      <div>%s</div>
    </div>
`, rule.Color, rule.Description)
	}
	fmt.Fprint(w, "</div>")
}

// 新的generateJSON支持颜色绑定
func generateJSON(data interface{}, currentPath []string, rules []HighlightRule, w io.Writer, indent int) {
	// 查找匹配的规则
	matchedRule := findMatchedRule(currentPath, rules)
	indentStr := strings.Repeat("  ", indent)

	// 添加高亮样式
	if matchedRule != nil {
		fmt.Fprintf(w, `<span style="background-color: %s;">`, matchedRule.Color)
	}

	switch v := data.(type) {
	case map[string]interface{}:
		handleObject(v, currentPath, rules, w, indentStr, indent)
	case []interface{}:
		handleArray(v, currentPath, rules, w, indentStr, indent)
	default:
		jsonBytes, _ := json.Marshal(v)
		fmt.Fprint(w, string(jsonBytes))
	}

	if matchedRule != nil {
		fmt.Fprint(w, `</span>`)
	}
}

// 查找第一个匹配的规则
func findMatchedRule(current []string, rules []HighlightRule) *HighlightRule {
	for _, rule := range rules {
		if comparePaths(current, rule.Path) {
			return &rule
		}
	}
	return nil
}

// 路径比较逻辑（支持通配符）
func comparePaths(current, target []string) bool {
	if len(target) == 0 {
		return false
	}

	// 全通配符匹配
	if len(target) == 1 && target[0] == "*" {
		return true
	}

	if len(current) != len(target) {
		return false
	}

	for i := range current {
		if target[i] != "*" && current[i] != target[i] {
			return false
		}
	}
	return true
}

// 处理JSON对象
func handleObject(obj map[string]interface{}, currentPath []string, rules []HighlightRule, w io.Writer, indentStr string, indent int) {
	fmt.Fprint(w, "{\n")
	i := 0
	for key, val := range obj {
		newPath := append(currentPath, key)
		fmt.Fprintf(w, "%s  %q: ", indentStr, key)
		generateJSON(val, newPath, rules, w, indent+1)
		if i < len(obj)-1 {
			fmt.Fprint(w, ",")
		}
		fmt.Fprint(w, "\n")
		i++
	}
	fmt.Fprintf(w, "%s}", indentStr)
}

// 处理JSON数组
func handleArray(arr []interface{}, currentPath []string, rules []HighlightRule, w io.Writer, indentStr string, indent int) {
	fmt.Fprint(w, "[\n")
	for i, elem := range arr {
		newPath := append(currentPath, fmt.Sprintf("[%d]", i))
		fmt.Fprintf(w, "%s  ", indentStr)
		generateJSON(elem, newPath, rules, w, indent+1)
		if i < len(arr)-1 {
			fmt.Fprint(w, ",")
		}
		fmt.Fprint(w, "\n")
	}
	fmt.Fprintf(w, "%s]", indentStr)
}

func formatPathStr(path []string) string {
	var result strings.Builder
	result.WriteString("$root")
	for i, p := range path {
		if i == 0 {
			// 第一个元素如果是数字，直接加上 []
			if _, err := fmt.Sscan(p, new(int)); err == nil {
				result.WriteString(fmt.Sprintf("[%v]", p))
			} else {
				result.WriteString(fmt.Sprintf("->%v", p))
			}
		} else {
			// 后续元素如果是数字，加上 []
			if _, err := fmt.Sscan(p, new(int)); err == nil {
				result.WriteString(fmt.Sprintf("[%v]", p))
			} else {
				result.WriteString(fmt.Sprintf("->%v", p))
			}
		}
	}
	return result.String()
}

func formatPath(path []string) []string {
	result := make([]string, 0)
	for _, item := range path {
		if isNumber(item) {
			result = append(result, "["+item+"]")
			continue
		}
		result = append(result, toInitialLower(item))
	}
	return result
}

func isNumber(s string) bool {
	_, err := strconv.Atoi(s) // 尝试将字符串转换为整数
	if err == nil {
		return true
	}
	return false
}

func toInitialLower(s string) string {
	if len(s) == 0 {
		return s
	}
	return strings.ToLower(string(s[0])) + s[1:]
}

func GenerateArrayHTMLFromString(ctx *gin.Context, arr []string, id int64) (path string, err error) {
	currentTime := time.Now().Format("2006-01-02_15:04:05")
	fileName := fmt.Sprintf("diff_%d_%s", id, currentTime)
	filePath := "./" + fileName + ".html"
	file, err := os.Create(filePath)
	if err != nil {
		fmt.Println("Error creating file:", err)
		return
	}
	defer func() {
		os.Remove(filePath) // 确保最后删除临时文件
		file.Close()
	}()

	// 解析差异项
	diffItems := ParseDiffItems(arr)
	stats := CalculateStats(diffItems)

	// 构建HTML模板
	var htmlBuilder strings.Builder
	
	// 写入HTML头部
	htmlBuilder.WriteString(`<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JSON Diff 可视化对比</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            line-height: 1.6;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 16px;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.2s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-2px);
        }
        
        .stat-number {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #666;
            font-size: 14px;
        }
        
        .stat-total .stat-number { color: #667eea; }
        .stat-create .stat-number { color: #28a745; }
        .stat-update .stat-number { color: #ffc107; }
        .stat-delete .stat-number { color: #dc3545; }
        
        .diff-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .diff-item {
            border-bottom: 1px solid #eee;
            transition: background-color 0.2s ease;
        }
        
        .diff-item:last-child {
            border-bottom: none;
        }
        
        .diff-item:hover {
            background-color: #f8f9fa;
        }
        
        .diff-header {
            padding: 16px 24px;
            background: #f8f9fa;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            user-select: none;
            transition: all 0.2s ease;
        }
        
        .diff-header:hover {
            background: #e9ecef;
        }
        
        .diff-info {
            display: flex;
            align-items: center;
            flex: 1;
        }
        
        .diff-path {
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            font-weight: 500;
            color: #2c3e50;
            font-size: 14px;
            line-height: 1.4;
        }
        
        .diff-type {
            padding: 6px 14px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            min-width: 60px;
            text-align: center;
        }
        
        .type-create { background: #d4edda; color: #155724; }
        .type-update { background: #fff3cd; color: #856404; }
        .type-delete { background: #f8d7da; color: #721c24; }
        
        .diff-content {
            padding: 24px;
            display: none;
            background: #ffffff;
        }
        
        .diff-content.expanded {
            display: block;
            animation: slideDown 0.2s ease-out;
        }
        
        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .diff-change {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .diff-from, .diff-to {
            padding: 16px;
            border-radius: 12px;
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            font-size: 13px;
            line-height: 1.5;
            white-space: pre-wrap;
            word-break: break-word;
            border: 1px solid;
            position: relative;
        }
        
        .diff-from {
            background: #fef2f2;
            border-color: #fecaca;
        }
        
        .diff-to {
            background: #f0fdf4;
            border-color: #bbf7d0;
        }
        
        .diff-label {
            font-weight: 600;
            margin-bottom: 8px;
            display: block;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            color: #6b7280;
        }
        
        .diff-value {
            color: #374151;
            font-size: 14px;
        }
        
        .toggle-icon {
            transition: transform 0.2s ease;
            display: inline-block;
            margin-right: 8px;
        }
        
        .toggle-icon.expanded {
            transform: rotate(90deg);
        }
        
        .no-diff {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }
        
        .no-diff-icon {
            font-size: 48px;
            margin-bottom: 20px;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 24px;
            }
            
            .stats {
                grid-template-columns: repeat(2, 1fr);
                gap: 10px;
            }
            
            .diff-change {
                grid-template-columns: 1fr;
                gap: 10px;
            }
            
            .diff-path {
                font-size: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 JSON Diff 可视化对比</h1>
            <p>清晰展示两个JSON版本之间的差异变化</p>
        </div>
        
        <div class="stats">
            <div class="stat-card stat-total">
                <div class="stat-number">`)
	htmlBuilder.WriteString(fmt.Sprintf("%d", stats.Total))
	htmlBuilder.WriteString(`</div>
                <div class="stat-label">总差异项</div>
            </div>
            <div class="stat-card stat-create">
                <div class="stat-number">`)
	htmlBuilder.WriteString(fmt.Sprintf("%d", stats.Create))
	htmlBuilder.WriteString(`</div>
                <div class="stat-label">新增</div>
            </div>
            <div class="stat-card stat-update">
                <div class="stat-number">`)
	htmlBuilder.WriteString(fmt.Sprintf("%d", stats.Update))
	htmlBuilder.WriteString(`</div>
                <div class="stat-label">修改</div>
            </div>
            <div class="stat-card stat-delete">
                <div class="stat-number">`)
	htmlBuilder.WriteString(fmt.Sprintf("%d", stats.Delete))
	htmlBuilder.WriteString(`</div>
                <div class="stat-label">删除</div>
            </div>
        </div>
        
        <div class="diff-container">`)

	if len(diffItems) == 0 {
		htmlBuilder.WriteString(`
            <div class="no-diff">
                <div class="no-diff-icon">✅</div>
                <h3>没有发现差异</h3>
                <p>两个JSON版本完全相同</p>
            </div>`)
	} else {
		for i, item := range diffItems {
			htmlBuilder.WriteString(generateDiffItemHTML(item, i))
		}
	}

	htmlBuilder.WriteString(`
        </div>
    </div>
    
    <script>
        function toggleDiff(index) {
            const content = document.getElementById('diff-content-' + index);
            const icon = document.getElementById('toggle-icon-' + index);
            const header = document.getElementById('diff-header-' + index);
            
            if (content.classList.contains('expanded')) {
                content.classList.remove('expanded');
                icon.classList.remove('expanded');
            } else {
                content.classList.add('expanded');
                icon.classList.add('expanded');
            }
        }
        
        // 添加键盘快捷键
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                // ESC键关闭所有展开的差异项
                document.querySelectorAll('.diff-content.expanded').forEach(content => {
                    content.classList.remove('expanded');
                });
                document.querySelectorAll('.toggle-icon.expanded').forEach(icon => {
                    icon.classList.remove('expanded');
                });
            }
        });
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('JSON Diff 可视化页面加载完成');
        });
    </script>
</body>
</html>`)

	// 写入HTML内容
	if _, err = file.WriteString(htmlBuilder.String()); err != nil {
		return "", err
	}

	// 上传到COS
	path, _, err = helpers.UploadFile2Cos(ctx, filePath, fileName, "html", "fwyyDiffRes")
	if err != nil {
		return
	}

	return
}

// ParseDiffItems 解析差异字符串数组
func ParseDiffItems(arr []string) []DiffItem {
	var items []DiffItem
	
	for _, str := range arr {
		item := parseSingleDiff(str)
		items = append(items, item)
	}
	
	return items
}

// parseSingleDiff 解析单个差异字符串
func parseSingleDiff(str string) DiffItem {
	// 格式: "路径, 类型, 旧值 --> 新值"
	parts := strings.Split(str, ", ")
	if len(parts) < 3 {
		return DiffItem{
			Type: "unknown",
			Raw:  str,
		}
	}
	
	path := parts[0]
	diffType := parts[1]
	
	// 分离旧值和新值
	valueParts := strings.Split(strings.Join(parts[2:], ", "), " --> ")
	var oldValue, newValue interface{}
	
	if len(valueParts) >= 1 {
		oldValue = parseValue(valueParts[0])
	}
	if len(valueParts) >= 2 {
		newValue = parseValue(valueParts[1])
	}
	
	return DiffItem{
		Path: path,
		Type: diffType,
		From: oldValue,
		To:   newValue,
		Raw:  str,
	}
}

// parseValue 解析值字符串
func parseValue(s string) interface{} {
	if s == "<nil>" {
		return nil
	}
	
	// 尝试解析为JSON
	var jsonValue interface{}
	if err := json.Unmarshal([]byte(s), &jsonValue); err == nil {
		return jsonValue
	}
	
	return s
}

// CalculateStats 计算差异统计
func CalculateStats(items []DiffItem) DiffStats {
	stats := DiffStats{}
	
	for _, item := range items {
		stats.Total++
		
		switch item.Type {
		case "create":
			stats.Create++
		case "update":
			stats.Update++
		case "delete":
			stats.Delete++
		}
	}
	
	return stats
}

// generateDiffItemHTML 生成单个差异项的HTML
func generateDiffItemHTML(item DiffItem, index int) string {
	fromStr := formatValue(item.From)
	toStr := formatValue(item.To)
	
	return fmt.Sprintf(`
            <div class="diff-item">
                <div class="diff-header" id="diff-header-%d" onclick="toggleDiff(%d)">
                    <div class="diff-info">
                        <span class="toggle-icon" id="toggle-icon-%d">▶</span>
                        <span class="diff-path">%s</span>
                    </div>
                    <span class="diff-type type-%s">%s</span>
                </div>
                <div class="diff-content" id="diff-content-%d">
                    <div class="diff-change">
                        <div class="diff-from">
                            <span class="diff-label">原值</span>
                            <div class="diff-value">%s</div>
                        </div>
                        <div class="diff-to">
                            <span class="diff-label">新值</span>
                            <div class="diff-value">%s</div>
                        </div>
                    </div>
                </div>
            </div>`, index, index, index, html.EscapeString(item.Path), item.Type, getTypeLabel(item.Type), index, html.EscapeString(fromStr), html.EscapeString(toStr))
}

// formatValue 格式化值显示
func formatValue(v interface{}) string {
	if v == nil {
		return "<nil>"
	}
	
	switch val := v.(type) {
	case string:
		return val
	default:
		if jsonData, err := json.MarshalIndent(val, "", "  "); err == nil {
			return string(jsonData)
		}
		return fmt.Sprintf("%v", v)
	}
}

// getTypeLabel 获取类型标签
func getTypeLabel(diffType string) string {
	switch diffType {
	case "create":
		return "新增"
	case "update":
		return "修改"
	case "delete":
		return "删除"
	default:
		return "未知"
	}
}

func addDiffTag(ctx *gin.Context, param string) string {
	var p map[string]interface{}
	err := json.Unmarshal([]byte(param), &p)
	if err != nil {
		return param
	}
	p["forDiff"] = 1
	newParam, err := json.MarshalToString(p)
	if err != nil {
		return param
	}
	return newParam
}

func addXUIDCookie(ctx *gin.Context, cookie string, xuid int64) string {
	cookie = cookie + ";isDebug=1"
	if xuid == 0 {
		return cookie
	}

	cookie = strings.TrimSpace(cookie)
	if cookie == "" {
		return "XUID=" + strconv.FormatInt(xuid, 10)
	}

	var (
		target = "XUID="
		newVal = target + strconv.FormatInt(xuid, 10)
		sb     strings.Builder
	)

	parts := strings.Split(cookie, ";")
	updated := false

	for i, p := range parts {
		kv := strings.SplitN(strings.TrimSpace(p), "=", 2)
		if len(kv) == 0 {
			continue
		}
		key := strings.TrimSpace(kv[0])
		if strings.EqualFold(key, "XUID") {
			// 替换
			sb.WriteString(newVal)
			updated = true
		} else {
			// 原样写回
			sb.WriteString(strings.TrimSpace(p))
		}

		// 加分号（最后一段除外）
		if i < len(parts)-1 {
			sb.WriteString("; ")
		}
	}

	// 如果原先没有 XUID，则追加
	if !updated {
		if sb.Len() > 0 {
			sb.WriteString("; ")
		}
		sb.WriteString(newVal)
	}

	return sb.String()
}

type PersonUidStruct struct {
	PersonUid int64 `json:"personUid"`
}

func getPersonUid(param string) int64 {
	p := PersonUidStruct{}
	err := json.Unmarshal([]byte(param), &p)
	if err != nil {
		return 0
	}
	return p.PersonUid
}

func buildDiffConfig(apiConfig stru.APIDiffConfig) DiffConfig {
	cfg := DiffConfig{
		IgnorePaths: apiConfig.IgnorePaths, // 直接使用 APIConfig 中的 IgnorePaths
		KeyMappers:  make(map[string]func(any) string),
		StringKeys:  make(map[string]bool),
	}

	for path, keyField := range apiConfig.IgnorePathArrayOrder {
		cfg.KeyMappers[path] = func(v any) string {
			mval, ok := v.(map[string]any)
			if !ok {
				return ""
			}
			return cast.ToString(mval[keyField])
		}
	}

	for _, key := range apiConfig.DiffString {
		cfg.StringKeys[key] = true
	}

	return cfg
}

func extractDataField(ctx *gin.Context, jsonStr string) (string, error) {
	var response struct {
		Data  interface{} `json:"data"`
		ErrNo int         `json:"errNo"`
	}

	if err := json.Unmarshal([]byte(jsonStr), &response); err != nil {
		return "", err
	}

	if response.ErrNo != 0 {
		return "", fmt.Errorf("errNo is not 0")
	}

	toString, err := json.MarshalToString(response.Data)
	if err != nil {
		return "", err
	}

	if toString == "[]" {
		return "", nil
	}

	jsoniter, err := components.Util.SortJSONKeysWithJsoniter(toString)
	if err != nil {
		zlog.Warnf(ctx, "sort json keys failed, err:%v, json:%s", err, toString)
		return toString, nil
	}

	return jsoniter, nil
}
