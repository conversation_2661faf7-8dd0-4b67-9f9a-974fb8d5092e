package diffJob

import (
	"encoding/json"
	"fwyytool/api/arkgo"
	"fwyytool/components"
	"fwyytool/consts"
	"fwyytool/service/tools/diffJob/diffRegister"
	"fwyytool/stru"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"time"
)

var DiffJobService diffJobService

type diffJobService struct {
}

func (d *diffJobService) Do(ctx *gin.Context) (err error) {
	defer func() {
		if err != nil {
			zlog.Errorf(ctx, "diffJobService error, err:%v", err)
		}

		if errPanic := recover(); errPanic != nil {
			zlog.Errorf(ctx, "diffJobService panic, err:%v", errPanic)
			components.Util.PanicTrace(ctx)
		}
	}()

	// 获取配置
	config, err := arkgo.NewClient().GetRDConfig(ctx, "assistantdesk_ark_student_list_data_diff_control_config")
	if err != nil {
		return err
	}
	var diffConfig = make(map[string]stru.APIDiffConfig)
	if len(config) == 0 {
		return err
	}

	err = json.Unmarshal([]byte(config), &diffConfig)
	if err != nil {
		return err
	}

	var lastID, handleTaskCount int64
	batchSize := 1000
	limit := 10000
	// 每天跑一天内的任务
	timeRange := time.Now().Add(-time.Duration(1) * 24 * time.Hour).Unix()
	// 回放任务
	for {
		var batch []stru.ArkStudentListDataDiff
		batch, err = arkgo.NewClient().GetDiffRecords(ctx, arkgo.GetDiffRecordsParams{
			UpdateTimeRange: timeRange,
			LastId:          lastID,
		})
		if err != nil {
			zlog.Errorf(ctx, "diffJobService GetDiffRecords error, err:%v", err)
			continue
		}

		for _, record := range batch {
			// 只处理初始化的
			if record.Status != 0 {
				continue
			}

			// 处理单个任务
			err = handleOneTask(ctx, record, diffConfig)
			if err != nil {
				zlog.Warnf(ctx, "handleOneTask error,taskID:%v err:%s", record.ID, err)
				err = arkgo.NewClient().UpdateDiffRecords(ctx, arkgo.UpdateDiffRecordsParams{
					ID: record.ID,
					Updates: map[string]interface{}{
						"status": consts.ArkStudentListDataDiffStatusFailed,
					},
				})
				if err != nil {
					zlog.Warnf(ctx, "handleOneTask update error,taskID:%v,handler:%v,type:%v, err:%s", record.ID, record.HandlerName, record.DiffType, err)
					continue
				}
				continue
			}
			handleTaskCount++
			if handleTaskCount > int64(limit) {
				zlog.Warnf(ctx, "handleOneTask today handle task reach limit, num:%v", handleTaskCount)
				break
			}
		}

		if len(batch) < batchSize {
			break
		}

		lastID = batch[len(batch)-1].ID
	}

	return
}

func handleOneTask(ctx *gin.Context, record stru.ArkStudentListDataDiff, diffConfig map[string]stru.APIDiffConfig) error {
	defer func() {
		if err := recover(); err != nil {
			zlog.Warnf(ctx, "diffJobService handleOneTask error, err:%s", err)
			components.Util.PanicTrace(ctx)
		}
	}()

	// diffType = 1 (原地回放)，没到预计执行时间，不处理
	if record.DiffType == consts.ArkStudentListDataDiffTypeUseOld {
		execDay := 7
		config, ok := diffConfig[record.HandlerName]
		if ok {
			execDay = config.ReExecutionDayAfter
		}
		targetTime := time.Unix(record.CreateTime, 0)
		timeAfterNDays := targetTime.AddDate(0, 0, execDay)
		if time.Now().Before(timeAfterNDays) {
			//zlog.Info(ctx, "AddStudentListDataDiffScript not reach time to exec :%v", record.ID)
			return nil
		}
	}

	zlog.Info(ctx, "diffJobService begin to exec :%v", record.ID)

	diffAPI := diffRegister.GetDataDiffAPI(record.HandlerName)
	if diffAPI == nil {
		err := arkgo.NewClient().UpdateDiffRecords(ctx, arkgo.UpdateDiffRecordsParams{
			ID: record.ID,
			Updates: map[string]interface{}{
				"status": consts.ArkStudentListDataDiffStatusNotHandle,
			},
		})
		if err != nil {
			return err
		}
		return nil
	}

	oldJson, err := diffAPI.GetOldData(ctx, &record, diffConfig)
	if err != nil {
		return err
	}

	newJson, err := diffAPI.GetNewData(ctx, &record, diffConfig)
	if err != nil {
		return err
	}

	diffNum, res, err := diffAPI.GetDiffResult(ctx, oldJson, newJson, record, diffConfig)
	if err != nil {
		zlog.Warnf(ctx, "diffJobService handle one task :%v,err:%+v", record.ID, err)
		return err
	}

	zlog.Info(ctx, "diffJobService handle one task :%v,diffNum:%v", record.ID, diffNum)

	if diffNum == 0 {
		err = arkgo.NewClient().UpdateDiffRecords(ctx, arkgo.UpdateDiffRecordsParams{
			ID: record.ID,
			Updates: map[string]interface{}{
				"status":   consts.ArkStudentListDataDiffStatusFinished,
				"diff_num": diffNum,
			},
		})
		if err != nil {
			return err
		}
		return nil
	}

	err = arkgo.NewClient().UpdateDiffRecords(ctx, arkgo.UpdateDiffRecordsParams{
		ID: record.ID,
		Updates: map[string]interface{}{
			"status":      consts.ArkStudentListDataDiffStatusFinished,
			"diff_num":    diffNum,
			"old_data":    oldJson,
			"new_data":    newJson,
			"diff_result": res,
		},
	})
	if err != nil {
		return err
	}

	time.Sleep(100 * time.Millisecond)

	return nil
}
