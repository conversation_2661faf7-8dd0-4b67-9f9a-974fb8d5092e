package tools

import (
	"fwyytool/api/arkgo"
	"fwyytool/components"
	"fwyytool/service/tools/diffJob"
	"net/http"

	"git.zuoyebang.cc/pkg/golib/v2/base"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
)

var DiffController diffController

type diffController struct {
}

func (s diffController) DoDiff(ctx *gin.Context) {
	err := diffJob.DiffJobService.Do(ctx)
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	base.RenderJsonSucc(ctx, "success")
	return
}

func (s diffController) DiffDetail(ctx *gin.Context) {
	ctx.HTML(http.StatusOK, "desk/datadiff/diffdetail.html", nil)
}

// GetDiffCount 获取差异统计数据
func (s diffController) GetDiffCount(ctx *gin.Context) {
	var param arkgo.GetArkStudentDataDiffResParam
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, errors.Wrap(components.ErrorParamInvalid, err.Error()))
		return
	}

	resp, err := arkgo.NewClient().GetDiffCountRes(ctx, param)
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	base.RenderJsonSucc(ctx, resp)
}

// GetDiffOverview 获取差异总览数据
func (s diffController) GetDiffOverview(ctx *gin.Context) {
	var param arkgo.GetArkStudentDataDiffResParam
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, errors.Wrap(components.ErrorParamInvalid, err.Error()))
		return
	}

	resp, err := arkgo.NewClient().GetDiffOverview(ctx, param)
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	base.RenderJsonSucc(ctx, resp)
}

// GetDiffRes 获取差异结果数据
func (s diffController) GetDiffRes(ctx *gin.Context) {
	var param arkgo.GetArkStudentDataDiffResParam
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, errors.Wrap(components.ErrorParamInvalid, err.Error()))
		return
	}

	resp, err := arkgo.NewClient().GetArkStudentDataDiffRes(ctx, param)
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	base.RenderJsonSucc(ctx, resp)
}
