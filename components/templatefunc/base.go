package templatefunc

import (
	"bytes"
	"encoding/json"
	"fmt"
	"github.com/gin-gonic/gin"
	"html/template"
	"math"
	"strconv"
	"time"
)

func SetFunc(engine *gin.Engine) {
	engine.SetFuncMap(template.FuncMap{
		"showTime": func(ts interface{}) string {
			str := ""
			switch ts := ts.(type) {
			case int64:
				if ts > 0 {
					str = time.Unix(ts, 0).Format("2006-01-02 15:04:05")
				}
			case float64:
				if int64(ts) > 0 {
					str = time.Unix(int64(ts), 0).Format("2006-01-02 15:04:05")
				}
			case int:
				if int64(ts) > 0 {
					str = time.Unix(int64(ts), 0).Format("2006-01-02 15:04:05")
				}
			case string:
				if v, err := strconv.ParseInt(ts, 10, 64); err == nil && v > 0 {
					str = time.Unix(v, 0).Format("2006-01-02 15:04:05")
				}
			default:
				str = ""
			}
			return str
		},
		"string": func(strbyte []byte) string {
			return string(strbyte)
		},
		"jsonPretty": func(jsonStr []byte) string {
			var out bytes.Buffer
			if err := json.Indent(&out, []byte(jsonStr), "", "\t"); err != nil {
				return string(jsonStr)
			}
			return fmt.Sprintf("%s\n", out.String())
		},
		"jsonPrettyStruct": func(jsonStruct interface{}) string {
			if jsonStruct == nil {
				return ""
			}
			marshal, _ := json.Marshal(jsonStruct)
			var out bytes.Buffer
			if err := json.Indent(&out, []byte(marshal), "", "\t"); err != nil {
				return string(marshal)
			}
			return fmt.Sprintf("%s\n", out.String())
		},
		"jsonPrettyStr": func(jsonStr string) string {
			var out bytes.Buffer
			if err := json.Indent(&out, []byte(jsonStr), "", "\t"); err != nil {
				return jsonStr
			}
			return fmt.Sprintf("%s\n", out.String())
		},
		"htmlUnescaped": func(html string) template.HTML {
			return template.HTML(html)
		},
		"getPageHtml": func(total interface{}, pageSize interface{}, curPage interface{}, requestParams, pageName string) string {
			fmt.Println("getPageHtml:", total, pageSize, curPage, requestParams, pageName)
			t := parseToInt(total)
			ps := parseToInt(pageSize)
			cp := parseToInt(curPage)
			totalPage := 0
			if t > 0 && ps > 0 {
				totalPage = int(math.Ceil(float64(t) / float64(ps)))
			}
			html := ""
			if totalPage > 0 {
				htmlTemp := `<nav aria-label="Page navigation"> <ul class="pagination"> %s %s <li>%s </li> </ul> </nav>`
				prePageHtml := `<li class="disabled"> <span> <span aria-hidden="true">&laquo;</span> </span> </li>`
				if cp > 1 {
					prePageHtml = fmt.Sprintf(`<li><a href="?%s&%s=%d"> <span aria-hidden="true">&laquo;</span> </a></li>`, requestParams, pageName, cp-1)
				}
				nextPageHtml := `<li class="disabled"> <span> <span aria-hidden="true">&raquo;</span> </span> </li>`
				if cp < totalPage {
					nextPageHtml = fmt.Sprintf(`<li><a href="?%s&%s=%d"> <span aria-hidden="true">&raquo;</span> </a></li>`, requestParams, pageName, cp+1)
				}
				pageHtml := ""
				for n := 1; n <= totalPage; n++ {
					if n == cp {
						pageHtml += fmt.Sprintf(`<li class="active"><span>%d<span class="sr-only">(current)</span></span></li>`, n)
					} else {
						pageHtml += fmt.Sprintf(`<li><a href="?%s&%s=%d"> <span aria-hidden="true">%d</span> </a></li>`, requestParams, pageName, n, n)
					}
				}
				html = fmt.Sprintf(htmlTemp, prePageHtml, pageHtml, nextPageHtml)
			}
			return html
		},
	})
}

func parseToInt(n interface{}) int {
	num := 0
	switch v := n.(type) {
	case float64:
		num = int(v)
	case int64:
		num = int(v)
	case string:
		num, _ = strconv.Atoi(v)
	case int:
		num = v
	}
	return num
}
