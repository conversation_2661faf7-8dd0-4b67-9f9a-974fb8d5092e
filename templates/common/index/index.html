<!DOCTYPE html>
<html lang="en">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>小工具 - 导航</title>
    <link rel="stylesheet" href="/fwyytool/assets/css/bootstrap.min.css" />
    <link rel="stylesheet" href="/fwyytool/assets/css/index.css" />
</head>

<body>
<div class="col_main">
    <div class="main_hd">
        <div id="topTab" class="title_tab">
            <ul class="title_tab_navs">
                <li data-id="common" class="title_tab_nav js_top selected"><a href="javascript:"> 首页 </a></li>
                <li data-id="desk" class="title_tab_nav js_top"><a href="javascript:"> 工作台 </a></li>
                <li data-id="userprofile" class="title_tab_nav js_top"><a href="javascript:"> 人管 </a></li>
                <li data-id="allocate" class="title_tab_nav js_top"><a href="javascript:"> 排灌班 </a></li>
                <li data-id="genke" class="title_tab_nav js_top"><a href="javascript:"> 跟课 </a></li>
                <li data-id="touchmsg" class="title_tab_nav js_top"><a href="javascript:"> 触达 </a></li>
                <li data-id="lpctransengine" class="title_tab_nav js_top"><a href="javascript:"> 绩效引擎 </a></li>
                <li data-id="pcassistant" class="title_tab_nav js_top"><a href="javascript:"> 批改 </a></li>
                <li data-id="datamis" class="title_tab_nav js_top"><a href="javascript:"> 数据 </a></li>
                <li data-id="newgoodsplatform" class="title_tab_nav js_top"><a href="javascript:"> 商品 </a></li>
                <li data-id="sellstrategy" class="title_tab_nav js_top"><a href="javascript:"> 策略 </a></li>
                <li data-id="orderbusiness" class="title_tab_nav js_top"><a href="javascript:"> 交易订单 </a></li>
                <li data-id="afterplat" class="title_tab_nav js_top"><a href="javascript:"> 售后 </a></li>
                <li data-id="fenxiao" class="title_tab_nav js_top"><a href="javascript:"> 分销 </a></li>
                <li data-id="assistantcourse" class="title_tab_nav js_top"><a href="javascript:"> 自主建课 </a></li>
                <li data-id="oplog" class="title_tab_nav js_top"><a href="javascript:"> 操作日志 </a></li>
                <li data-id="zerotrans" class="title_tab_nav js_top"><a href="javascript:"> 0转正 </a></li>
                <li data-id="plugin" class="title_tab_nav js_top"><a href="javascript:"> 插件 </a></li>
            </ul>
        </div>
    </div>

    <!-- common页面 -->
    <div id="common" class="main_bd" style="display: block">
        <ul class="weburl clearfix" style="margin-top: 10px;">
            <li>
                <label style="font-weight: bold;color: #d9534f;">业务监控</label>
            </li>
            <li>
                <a href="https://thanos.op.zuoyebang.cc/d/2MBpS7BIk/fu-wu-yun-ying-shi-shi-da-pan?search=open&folder=current&orgId=1" target="_blank">实时监控导航</a>
            </li>
            <li>
                <a href="https://thanos.op.zuoyebang.cc/d/2MBpS7BIk/fu-wu-yun-ying-shi-shi-da-pan?orgId=1" target="_blank">服务运营服务大盘</a>
            </li>
            <li>
                <a href="https://thanos.op.zuoyebang.cc/d/vHLyZ3LIz/gong-zuo-tai-ye-wu-jian-kong" target="_blank">工作台业务监控</a>
            </li>
            <li>
                <a href="https://thanos.op.zuoyebang.cc/d/MHSRHnBIz/hong-da-shi-shi-jian-kong?orgId=1" target="_blank">触达</a>
            </li>
            <li>
                <a href="https://thanos.op.zuoyebang.cc/d/ip06S7fIk/gen-ke-shi-shi-jian-kong?orgId=1" target="_blank">跟课</a>
            </li>
        </ul>

        <ul class="weburl clearfix" style="margin-top: 10px;">
            <li>
                <label style="font-weight: bold;color: #40AFFE;">服务运营</label>
            </li>
            <li>
                <a href="https://assistantdesk.zuoyebang.cc" target="_blank">教师工作站（线上）</a>
            </li>
            <li>
                <a href="https://assistantdesk-base-cc.suanshubang.cc" target="_blank">教师工作站（线下）</a>
            </li>
            <li>
                <a href="https://assistantdesk.zuoyebang.cc/scmis/view/" target="_blank">教师工作站旧版（线上）</a>
            </li>
            <li>
                <a href="https://assistantdesk-base-cc.suanshubang.cc/scmis/view/">教师工作站旧版（线下）</a>
            </li>
            <li>
                <a href="https://pigai.zuoyebang.com/pcassistant/view" target="_blank">批改系统（线上）</a>
            </li>
            <li>
                <a href="https://pigai-base-e.suanshubang.cc/pcassistant/view/" target="_blank">批改系统（线下）</a>
            </li>
            <li>
                <a href="https://touchmis.zuoyebang.cc/static/touch/#/config" target="_blank">触达配置系统（线上）</a>
            </li>
            <li>
                <a href="https://touchmis-base-cc.suanshubang.cc/static/touch/#/config" target="_blank">触达配置系统（线下）</a>
            </li>
        </ul>

        <ul class="weburl clearfix" style="margin-top: 10px;">
            <li>
                <label style="font-weight: bold;color: #409c19;">售卖系统</label>
            </li>
            <li>
                <a href="https://sellmis.zuoyebang.cc/static/sellgoods/index.html#/" target="_blank">售卖平台（线上）</a>
            </li>
            <li>
                <a href="https://sellmis-base-cc.suanshubang.cc/static/sellgoods/index.html#/" target="_blank">售卖平台（线下）</a>
            </li>
            <li>
                <a href="https://kf.zuoyebang.cc/misservice/platform/home/<USER>" target="_blank">客服平台（线上）</a>
            </li>
            <li>
                <a href="https://kf-base-cc.suanshubang.cc/misservice/platform/home/<USER>" target="_blank">客服平台（线下）</a>
            </li>
            <li>
                <a href="https://actmis.zuoyebang.cc/static/senior-marketing/#/fenxiao/distribution/activity/list" target="_blank">活动营销平台（线上）</a>
            </li>
            <li>
                <a href="https://actmis-base-cc.suanshubang.cc/static/senior-marketing/#/fenxiao/distribution/activity/list" target="_blank">活动营销平台（线下）</a>
            </li>
            <li>
                <a href="https://actmis.zuoyebang.cc/static/niffler/index.html#/activity-square/index" target="_blank">蜂鸟活动平台（线上）</a>
            </li>
            <li>
                <a href="https://actmis-base-cc.suanshubang.cc/static/niffler/index.html#/activity-square/index" target="_blank">蜂鸟活动平台（线下）</a>
            </li>

        </ul>

        <ul class="weburl clearfix" style="margin-top: 10px;">
            <li>
                <label style="font-weight: bold;color: #cc5500;">应用平台</label>
            </li>
            <li>
                <a href="https://tianhuo.zuoyebang.cc/static/dataFactoryFE/#/home" target="_blank">⭐️天火平台</a>
            </li>
            <li>
                <a href="https://beidou.zuoyebang.cc/zbk/student" target="_blank">⭐️北斗平台</a>
            </li>
            <li>
                <a href="https://op.zuoyebang.cc" target="_blank">运维平台（线上）</a>
            </li>
            <li>
                <a href="https://ship.zuoyebang.cc/#/envlist" target="_blank">运维平台（线下）</a>
            </li>
            <li>
                <a href="https://git.zuoyebang.cc" target="_blank">Git代码平台</a>
                <a href="javascript:showCommon('gitinfo')" class="helper-target"></a>
            </li>
            <li>
                <a href="https://pms.zuoyebang.cc/page" target="_blank">PMS需求管理平台</a>
            </li>
            <li>
                <a href="https://zbfb.zuoyebang.cc/#/publish" target="_blank">集中发布平台</a>
            </li>
            <li>
                <a href="https://dba.zuoyebang.cc/#/order" target="_blank">数据库运维平台</a>
            </li>
            <li>
                <a href="https://udaplus.zuoyebang.cc/#" target="_blank">uda离线数据平台</a>
            </li>
            <li>
                <a href="https://zlink.zuoyebang.cc/#/overview-list" target="_blank">zlink在线数据平台</a>
                <a href="javascript:showCommon('zlinkinfo')" class="helper-target"></a>
            </li>
            <li>
                <a href="https://datamap.zuoyebang.cc/#/home" target="_blank">数据仓库平台</a>
            </li>
            <li>
                <a href="https://dba.zuoyebang.cc/#/order" target="_blank">蓝鲸平台</a>
            </li>
            <li>
                <a href="https://rds.zuoyebang.cc/index" target="_blank">RDS后台地址（将要废弃）</a>
            </li>
        </ul>

        <ul class="weburl clearfix" style="margin-top: 10px;">
            <li>
                <label style="font-weight: bold;color: #8D8D8D;">工具平台</label>
            </li>
            <li>
                <a href="https://yapi.zuoyebang.cc/group/12073" target="_blank">YApi管理</a>
            </li>
            <li>
                <a href="https://ssv.zuoyebang.cc/static/open-sell/index.html#/index" target="_blank">售卖服务平台</a>
            </li>
            <li>
                <a href="https://ued.zuoyebang.cc/documents/docs/dds/index.html" target="_blank">售卖数据字典</a>
            </li>
            <li>
                <a href="https://assistantdesk.zuoyebang.cc/assistanttool#/" target="_blank">工作台工具站</a>
            </li>
            <li>
                <a href="https://assistantdesk.zuoyebang.cc/coursetrans/tools/config" target="_blank">工作台duxuesc配置列表（线上）</a>
            </li>
            <li>
                <a href="https://assistantdesk-base-cc.suanshubang.cc/coursetrans/tools/config" target="_blank">工作台duxuesc配置列表（线下）</a>
            </li>
            <li>
                <a id="common-es-online" href="https://kibana.zuoyebang.cc/8032/app/dev_tools#/console" target="_blank">ES工具平台（线上）</a>
                <a href="javascript:showCommon('esonline')" class="helper-target"></a>
            </li>
            <li>
                <a href="https://kibana.zuoyebang.cc/8705/app/dev_tools#/console" target="_blank">ES工具平台（线下）</a>
                <a href="javascript:showCommon('esoffline')" class="helper-target"></a>
            </li>
            <li>
                <a href="https://devmis.zuoyebang.cc/static/mis-square-platform-fe/?#/fnmis/user" target="_blank">蜂鸟工具平台（线上）</a>
            </li>
        </ul>

        <ul class="weburl clearfix" style="margin-top: 10px;">
            <li>
                <label style="font-weight: bold;color: #2e6da4;">文档链接</label>
            </li>
            <li>
                <a href="https://alidocs.dingtalk.com/i/nodes/ZX6GRezwJl7DEo6BHkE29OeoVdqbropQ" target="_blank">上线计划及排班</a>
            </li>
            <li>
                <a href="https://docs.zuoyebang.cc/doc/1826968370924094437?ddtab=true" target="_blank">🔥日常迭代进度表</a>
            </li>
            <li>
                <a href="https://sinan.zuoyebang.cc/settings/business/list" target="_blank">值班表</a>
            </li>
            <li>
                <a href="https://wiki.zuoyebang.cc/pages/viewpage.action?pageId=297558278" target="_blank">测试环境数据库拆分</a>
                <a href="javascript:showCommon('testdb')" class="helper-target"></a>
            </li>
            <li>
                <a href="https://wiki.zuoyebang.cc/pages/viewpage.action?pageId=504582196" target="_blank">🔥技术方案模板</a>
            </li>
        </ul>
        <div id="common-window" class="tips-window" hidden="hidden">
            <div>content</div>
            <div class="tips-close" onclick="hiddenWin('common-window')">关闭</div>
        </div>
    </div>

    <!-- desk页面 -->
    <div id="desk" class="main_bd" style="display: none">
        <ul class="weburl clearfix" style="margin-top: 10px;">
            <li>
                <label style="font-weight: bold;color: #276235;">常用导航</label>
            </li>
            <li>
                <a href="https://wiki.zuoyebang.cc/pages/viewpage.action?pageId=331237571" target="_blank">上线计划及排班</a>
            </li>
            <li>
                <a href="https://wiki.zuoyebang.cc/pages/viewpage.action?pageId=468911753" target="_blank">日常迭代进度表</a>
            </li>
            <li>
                <a href="https://sinan.zuoyebang.cc/settings/business/list" target="_blank">值班表</a>
            </li>
        </ul>

        <ul class="weburl clearfix" style="margin-top: 10px;">
            <li>
                <label style="font-weight: bold;color: #276235;">监控大盘</label>
            </li>
            <li>
                <a href="https://op.zuoyebang.cc/static/odin/#/monitor/screen/9631" target="_blank">genke大盘</a>
            </li>
        </ul>

        <ul class="weburl clearfix" style="margin-top: 10px;">
            <li>
                <label style="font-weight: bold;color: #f30e06;">基础工具</label>
            </li>
            <li>
                <a href="/fwyytool/common/tools/idcode" target="_blank">ID加解密</a>
                <a href="/fwyytool/tools/mysql/slowstat" target="_blank">慢查询监控</a>
                <a href="/fwyytool/assets/httptest/index.html?timestamp={{.timestamp}}" target="_blank">HttpTest</a>
            </li>
        </ul>

        <ul class="weburl clearfix" style="margin-top: 10px;">
            <li>
                <label style="font-weight: bold;color:  rgb(138, 43, 226);">课程章节</label>
            </li>
            <li>
                <a href="/fwyytool/desk/course/detail" target="_blank">课程详情</a>
            </li>
        </ul>

        <ul class="weburl clearfix" style="margin-top: 10px;">
            <li>
                <label style="font-weight: bold;color: #f30e06;">方舟</label>
            </li>
            <li>
                <a href="/fwyytool/desk/ark/detail" target="_blank">课程方舟详情</a>
            </li>
            <li>
                <a href="/fwyytool/desk/ark/jumptasklist" target="_blank">任务列表跳转工具</a>
            </li>
            <li>
                <a href="/fwyytool/desk/downgrade/getconfig" target="_blank">方舟字段降级工具</a>
            </li>
            <li>
                <a href="/fwyytool/desk/downgrade/getfusing" target="_blank">新方舟字段监控工具</a>
            </li>
        </ul>

        <ul class="weburl clearfix" style="margin-top: 10px;">
            <li>
                <label style="font-weight: bold;color: #7f4e00;">方舟测试case查询</label>
            </li>
            <li>
                <a href="/fwyytool/desk/arktestcase/rule" target="_blank">规则id查询</a>
                <a href="/fwyytool/desk/arktestcase/cname" target="_blank">cname查询</a>
                <a href="/fwyytool/desk/arktestcase/tool" target="_blank">功能工具ID查询</a>
            </li>
        </ul>

        <ul class="weburl clearfix" style="margin-top: 10px;">
            <li>
                <label style="font-weight: bold;color: #7f4e00;">学生列表流量回放工具</label>
            </li>
            <li>
                <a href="/fwyytool/desk/datadiff/diffdetail" target="_blank">回放结果</a>
            </li>
        </ul>

        <ul class="weburl clearfix" style="margin-top: 10px;">
            <li>
                <label style="font-weight: bold;color: #f30e06;">缓存清理工具</label>
            </li>
            <li>
                <a href="/fwyytool/desk/cachetool/delarkcache" target="_blank">清理方舟字段缓存</a>
            </li>
            <li>
                <a href="/fwyytool/desk/cachetool/delcachebykey" target="_blank">清理缓存</a>
            </li>
        </ul>

        <ul class="weburl clearfix" style="margin-top: 10px;">
            <li>
                <label style="font-weight: bold;color: #4A203B;">北斗工具</label>
            </li>
            <li>
                <a href="https://beidou.zuoyebang.cc/zbk/student" target="_blank">学生信息</a>
                <a href="https://beidou.zuoyebang.cc/zbk/course" target="_blank">课程信息</a>
                <a href="https://beidou.zuoyebang.cc/zbk/lesson" target="_blank">章节信息</a>
            </li>
        </ul>

        <ul class="weburl clearfix" style="margin-top: 10px;">
            <li>
                <label style="font-weight: bold;color: #03a9f4;">跟课</label>
            </li>
            <li>
                <a href="/fwyytool/genke/attend/detail" target="_blank">进出直播间记录</a>
            </li>
        </ul>

        <ul class="weburl clearfix" style="margin-top: 10px;">
            <li>
                <label style="font-weight: bold;color: #03a9f4;">学科诊断</label>
            </li>
            <li>
                <a href="/fwyytool/desk/fwyyevaluate/textconfig" target="_blank">文案配置</a>
            </li>
            <li>
                <a href="/fwyytool/desk/fwyyevaluate/coursemapping" target="_blank">课程问卷绑定</a>
            </li>
        </ul>
    </div>

    <!-- allocate页面-->
    <div id="allocate" class="main_bd" style="display: none">
        <ul class="weburl clearfix" style="margin-top: 10px;">
            <li>
                <label style="font-weight: bold;color: #cc5500;">接口API工具</label>
            </li>
            <li>
                <a href="/fwyytool/allocate/apis/getcourseinfo" target="_blank">课程id获取已排班资产信息</a>
            </li>
        </ul>
    </div>

    <!-- 商品页面-->
    <div id="newgoodsplatform" class="main_bd" style="display: none">
        <ul class="weburl clearfix" style="margin-top: 10px;">
            <li>
                <label style="font-weight: bold;color: #cc5500;">接口API工具</label>
            </li>
            <li>
                <a href="/fwyytool/newgoodsplatform/skucourse/getskuinfobycourseid" target="_blank">课程id获取商品信息</a>
            </li>
            <li>
                <a href="/fwyytool/newgoodsplatform/skucourse/getcourseinfobyskuid" target="_blank">商品id获取课程信息</a>
            </li>
        </ul>
    </div>

    <!-- 交易订单页面-->
    <div id="orderbusiness" class="main_bd" style="display: none">
        <ul class="weburl clearfix" style="margin-top: 10px;">
            <li>
                <label style="font-weight: bold;color: #cc5500;">接口API工具</label>
            </li>
            <li>
                <a href="/fwyytool/orderbusiness/order/getuserorderlist" target="_blank">用户uid查询全部订单</a>
            </li>
        </ul>
    </div>

    <!-- 自主建课页面 -->
    <div id="assistantcourse" class="main_bd" style="display: none">
        <ul class="weburl clearfix" style="margin-top: 10px;">
            <li>
                <label style="font-weight: bold;color: #cc5500;">接口API工具</label>
            </li>
            <li>
                <a href="/fwyytool/assistantcourse/apis/tasklist">真人id获取任务列表</a>
            </li>
        </ul>
    </div>

    <!-- 操作日志工具 -->
    <div id="oplog" class="main_bd" style="display: none">
        <ul class="weburl clearfix" style="margin-top: 10px;">
            <li>
                <label style="font-weight: bold;color: #cc5500;">接口API工具</label>
            </li>
            <li>
                <a href="/fwyytool/desk/oplog/list">记录查询</a>
            </li>
        </ul>
    </div>

    <!-- touchmsg页面 -->
    <div id="touchmsg" class="main_bd" style="display: none">
        <ul class="weburl clearfix" style="margin-top: 10px;">
            <li>
                <label style="font-weight: bold;color: #276235;">小工具</label>
            </li>
            <li>
                <a href="/fwyytool/tools/gray/checkclusterinfo" target="_blank">组织人员大促检测工具</a>
            </li>
            <li>
                <a href="/fwyytool/touchmis/tools/kms/decryptAndEncrypt" target="_blank">KMS 加解密</a>
            </li>
            <li>
                <a href="/fwyytool/touchmis/tools/touch/getCallRecordInfo" target="_blank">外呼结果查询</a>
            </li>
            <li>
                <a href="/fwyytool/touchmis/tools/touch/checkSmsTpl" target="_blank">验证短信模板</a>
            </li>
        </ul>
        <ul class="weburl clearfix" style="margin-top: 10px;">
            <li>
                <label style="font-weight: bold;color: #276235;">外部工具</label>
            </li>
            <li>
                <a href="/fwyytool/touchmis/tools/kp/checkFriend" target="_blank">资产好友关系查询</a>
            </li>
            <li>
                <a href="/fwyytool/touchmis/tools/kp/getSendQueueLength" target="_blank">鲲鹏侧积压情况查询</a>
            </li>
        </ul>
    </div>

    <!-- 0转正 -->
    <div id="zerotrans" class="main_bd" style="display: none">
        <ul class="weburl clearfix" style="margin-top: 10px;">

        </ul>
    </div>

    <!-- 插件页面 -->
    <div id="plugin" class="main_bd" style="display: none">
        <ul class="weburl clearfix" style="margin-top: 10px;">
            <li>
                <label style="font-weight: bold;color: #2e6da4;">可下载插件</label>
            </li>
            <li id="plugin-loading" style="color: #666;">
                ⏳ 正在加载插件列表...
            </li>
            <div id="plugin-list" style="display: none;"></div>
        </ul>

        <div style="margin-top: 20px; padding: 15px; background-color: #f8f9fa; border-left: 4px solid #2e6da4; border-radius: 4px;">
            <h4 style="margin: 0 0 15px 0; color: #2e6da4; font-size: 16px;">📋 插件安装使用说明</h4>

            <div style="margin-bottom: 15px;">
                <h5 style="margin: 0 0 8px 0; color: #333; font-size: 14px;">📥 下载插件</h5>
                <ul style="margin: 0; padding-left: 20px; color: #666;">
                    <li style="margin: 3px 0;">点击上方下载链接获取插件文件</li>
                    <li style="margin: 3px 0;">将下载的插件文件解压到一个<strong style="color: #d9534f;">固定的文件夹</strong>中（如：桌面/插件文件夹）</li>
                </ul>
            </div>

            <div style="margin-bottom: 15px;">
                <h5 style="margin: 0 0 8px 0; color: #333; font-size: 14px;">🔧 安装插件</h5>
                <div style="margin-bottom: 10px;">
                    <strong style="color: #2e6da4;">Chrome/Edge 浏览器：</strong>
                    <ol style="margin: 5px 0; padding-left: 20px; color: #666;">
                        <li style="margin: 2px 0;">打开浏览器，在地址栏输入 <code style="background: #e9ecef; padding: 2px 4px; border-radius: 3px;">chrome://extensions/</code> 或 <code style="background: #e9ecef; padding: 2px 4px; border-radius: 3px;">edge://extensions/</code></li>
                        <li style="margin: 2px 0;">开启右上角的"开发者模式"开关</li>
                        <li style="margin: 2px 0;">点击"加载已解压的扩展程序"</li>
                        <li style="margin: 2px 0;">选择解压后的插件文件夹（包含 manifest.json 的文件夹）</li>
                        <li style="margin: 2px 0;"><strong style="color: #d9534f;">⚠️ 重要：</strong>安装完成后，<strong style="color: #d9534f;">不要移动或删除解压的插件文件夹</strong>，否则插件将无法正常工作</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>


</div>
<script src="/fwyytool/assets/js/jquery-2.1.4.min.js"></script>
<script>
    $(document).ready(function () {
        $("#topTab li.title_tab_nav").off("click").on("click", function () {
            let id = $(this).data("id");
            $(this).siblings(".selected").removeClass("selected");
            $(this).addClass("selected");
            $(this).siblings().each(function () {
                $("#" + $(this).data("id")).hide();
            })
            $("#" + id).show();

            // 当切换到插件页面时，加载插件列表
            if (id === "plugin") {
                loadPluginList();
            }
        });

        // 页面加载时如果默认显示插件页面，也要加载插件列表
        if ($("#plugin").is(":visible")) {
            loadPluginList();
        }
    });

    function showWin(pageWin, content) {
        let pageDom = $("#" + pageWin);
        pageDom[0].childNodes.forEach(item => {
            if (item.nodeType === 1 && item.className !== "tips-close") {
                item.innerHTML = content;
            }
        });
        pageDom.show();
    }

    function hiddenWin(pageWin) {
        let pageDom = $("#" + pageWin);
        pageDom[0].childNodes.forEach(item => {
            if (item.nodeType === 1 && item.className !== "tips-close") {
                item.innerHTML = '';
            }
        });
        pageDom.hide();
    }

    // common
    function showCommon(tool) {
        let content = "";
        switch (tool) {
            case "esonline":
                content = "<label>用户名：es_rd_r</label><br><label>密码：yCYGzvFXZIHDTjmBVulKo</label><br><a href='https://wiki.zuoyebang.cc/pages/viewpage.action?pageId=339922929'>ES索引规范</a>";
                break;
            case "esoffline":
                content = "<label>用户名：ship_test</label><br><label>密码：ship_test</label><br><a href='https://wiki.zuoyebang.cc/pages/viewpage.action?pageId=339922929'>ES索引规范</a>";
                break;
            case "zlinkinfo":
                content = "<a href='https://wiki.zuoyebang.cc/pages/viewpage.action?pageId=312658803'>zlink权限申请文档</a>";
                break;
            case "testdb":
                content = "<table style='border-collapse: collapse'>" +
                    "<thead><tr><th>数据库名</th><th>IP地址</th><th>用户名</th><th>密码</th><th>包含数据库</th></tr></thead>" +
                    "<tbody>" +
                    "<tr><td>质量中心-直播课服务运营测试-测试</td><td>************:6060</td><td>homework</td><td>homework</td><td>duxuesc<br>lpc reach</td></tr>" +
                    "<tr><td>质量中心-公共-测试</td><td>*************:3306</td><td>homework</td><td>homework</td><td>fudao</td></tr>" +
                    "</tbody>" +
                    "</table>";
                break;
            case "gitinfo":
                content = "<a href='https://pms.zuoyebang.cc/ApplyForGit?tabname=namecodegitforgroup'>Git权限申请</a>";
                break;
        }

        if (content.length === 0) {
            return
        }

        showWin("common-window", content);
    }

    // 加载插件列表
    function loadPluginList() {
        // 如果已经加载过，直接返回
        if ($("#plugin-list").children().length > 0) {
            return;
        }

        $.ajax({
            url: '/fwyytool/common/download/list',
            method: 'GET',
            headers: {
                'Accept': 'application/json'
            },
            dataType: 'json',
            success: function(response) {
                displayPluginFiles(response.files || []);
            },
            error: function() {
                displayPluginError();
            }
        });
    }

    // 显示插件文件列表
    function displayPluginFiles(files) {
        const loadingElement = $("#plugin-loading");
        const listElement = $("#plugin-list");

        loadingElement.hide();

        if (files.length === 0) {
            listElement.html(`
                <li style="color: #999; font-style: italic;">
                    📭 暂无可下载的插件文件
                </li>
            `);
        } else {
            let fileListHtml = '';
            files.forEach(function(file) {
                fileListHtml += `
                    <li>
                        <a href="/fwyytool/common/download/plugin?file=${encodeURIComponent(file.name)}"
                           target="_blank"
                           title="文件大小: ${formatFileSize(file.size)}, 修改时间: ${file.modTime}">
                            📦 下载 ${file.name}
                        </a>
                    </li>
                `;
            });
            listElement.html(fileListHtml);
        }

        listElement.show();
    }

    // 显示插件加载错误
    function displayPluginError() {
        const loadingElement = $("#plugin-loading");
        const listElement = $("#plugin-list");

        loadingElement.hide();
        listElement.html(`
            <li style="color: #d9534f;">
                ❌ 加载插件列表失败，请刷新页面重试
            </li>
        `);
        listElement.show();
    }

    // 格式化文件大小
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
</script>
</body>
</html>